import { getConfigValue } from '../config/config';
import type { WebSocketDependencies, SessionConfig } from '../types/websocket';

// 1. OUTBOUND CALL Configuration
export async function getOutboundCallConfig(deps: WebSocketDependencies, callSid?: string): Promise<SessionConfig> {
    // CRITICAL FIX: Wait for stored config with retry mechanism to handle race conditions
    const logSid = callSid ? `[${callSid}] ` : '';
    console.log(`🔍 ${logSid}[OUTBOUND] Waiting for stored configuration from make-call endpoint...`);
    const configStartTime = Date.now();
    
    const storedConfig = await waitForStoredConfig(deps, callSid);
    if (storedConfig) {
        const configLoadTime = Date.now() - configStartTime;
        console.log(`✅ ${logSid}[OUTBOUND] Using stored config from make-call with script: ${storedConfig.scriptId} (loaded in ${configLoadTime}ms)`);
        
        // Validate AI instructions are present
        if (!storedConfig.aiInstructions || storedConfig.aiInstructions.trim().length === 0) {
            console.error(`❌ [OUTBOUND] Stored config missing AI instructions - this will cause session creation to fail`);
            console.error(`❌ [OUTBOUND] Config details:`, {
                hasConfig: !!storedConfig,
                hasAiInstructions: !!storedConfig.aiInstructions,
                instructionLength: storedConfig.aiInstructions?.length || 0,
                scriptId: storedConfig.scriptId
            });
        } else {
            console.log(`✅ [OUTBOUND] Stored config has valid AI instructions (${storedConfig.aiInstructions.length} chars)`);
        }
        
        return storedConfig;
    }
    
    console.log(`⚠️ [OUTBOUND] No stored config available after timeout, falling back to script manager`);
    const fallbackStartTime = Date.now();

    // Fallback: try to get current outbound script
    try {
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        if (currentScript) {
            const config = await deps.scriptManager.getScriptConfig(currentScript, false);
            if (config) {
                const fallbackLoadTime = Date.now() - fallbackStartTime;
                console.log(`✅ [OUTBOUND] Using current outbound script: ${currentScript} (fallback loaded in ${fallbackLoadTime}ms)`);
                return {
                    ...config,
                    scriptId: String(config.scriptId),
                    targetName: null,
                    targetPhoneNumber: null
                };
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting outbound script config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Last resort: default outbound config
    console.warn('⚠️ [OUTBOUND] Using fallback config - no script found');
    return {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: validVoice,
        model: validModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'outbound',
        scriptId: 'default'
    };
}

// Helper function to wait with retry logic for stored configuration
async function waitForStoredConfig(deps: WebSocketDependencies, callSid?: string, maxRetries: number = 5, retryDelayMs: number = 200): Promise<any> {
    let attempts = 0;
    
    while (attempts < maxRetries) {
        if (deps.getNextCallConfig) {
            // ATOMIC RETRIEVAL: Try call-specific config first, then fallback to global
            const storedConfig = await deps.getNextCallConfig(callSid);
            if (storedConfig && storedConfig.aiInstructions) {
                const configSource = callSid ? 'call-specific' : 'global';
                console.log(`✅ Configuration found (${configSource}) after ${attempts} attempts (${attempts * retryDelayMs}ms)`);
                return storedConfig;
            }
        }
        
        attempts++;
        if (attempts < maxRetries) {
            const logSid = callSid ? `[${callSid}] ` : '';
            console.log(`⏳ ${logSid}Configuration not ready, retrying in ${retryDelayMs}ms (attempt ${attempts}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        }
    }
    
    const logSid = callSid ? `[${callSid}] ` : '';
    console.warn(`⚠️ ${logSid}Configuration not available after ${maxRetries} attempts (${maxRetries * retryDelayMs}ms timeout)`);
    return null;
}

// 2. INBOUND CALL Configuration
export async function getInboundCallConfig(deps: WebSocketDependencies, callSid?: string): Promise<SessionConfig> {
    // CRITICAL FIX: Wait for stored config with retry mechanism to handle race conditions
    const logSid = callSid ? `[${callSid}] ` : '';
    console.log(`🔍 ${logSid}[INBOUND] Waiting for stored configuration from webhook...`);
    const configStartTime = Date.now();
    
    const storedConfig = await waitForStoredConfig(deps, callSid);
    if (storedConfig) {
        const configLoadTime = Date.now() - configStartTime;
        console.log(`✅ [INBOUND] Using stored config from webhook with script: ${storedConfig.scriptId || 'unknown'} (loaded in ${configLoadTime}ms)`);
        
        // Validate AI instructions are present
        if (!storedConfig.aiInstructions || storedConfig.aiInstructions.trim().length === 0) {
            console.error(`❌ [INBOUND] Stored config missing AI instructions - this will cause session creation to fail`);
            console.error(`❌ [INBOUND] Config details:`, {
                hasConfig: !!storedConfig,
                hasAiInstructions: !!storedConfig.aiInstructions,
                instructionLength: storedConfig.aiInstructions?.length || 0,
                scriptId: storedConfig.scriptId
            });
        } else {
            console.log(`✅ [INBOUND] Stored config has valid AI instructions (${storedConfig.aiInstructions.length} chars)`);
        }
        
        // Ensure it's marked as inbound call
        storedConfig.isIncomingCall = true;
        storedConfig.scriptType = storedConfig.scriptType || 'inbound';
        return storedConfig;
    }
    
    console.log(`⚠️ [INBOUND] No stored config available after timeout, falling back to script manager`);
    const fallbackStartTime = Date.now();

    // Fallback: Try to get current incoming script
    try {
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        if (currentScript) {
            const scriptConfig = await deps.scriptManager.getScriptConfig(currentScript, true);
            if (scriptConfig && scriptConfig.aiInstructions) {
                const fallbackLoadTime = Date.now() - fallbackStartTime;
                console.log(`✅ [INBOUND] Using current incoming script: ${currentScript} (fallback loaded in ${fallbackLoadTime}ms)`);
                return {
                    ...scriptConfig,
                    scriptId: String(scriptConfig.scriptId)
                };
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting inbound script config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice('Kore'); // Default voice for inbound
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Last resort: Try to load a default incoming campaign script
    try {
        // Load campaign script 7 (incoming campaign 1) as default
        const defaultScript = await deps.scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
        if (defaultScript && defaultScript.aiInstructions) {
            console.log(`✅ [INBOUND] Using default incoming campaign script 1`);
            return {
                ...defaultScript,
                scriptId: String(defaultScript.scriptId)
            };
        }
    } catch (scriptError) {
        console.error('❌ Error loading default incoming campaign script:', scriptError);
    }

    // Final fallback to minimal config (should not happen in production)
    console.warn('⚠️ [INBOUND] Using minimal fallback config - this should not happen in production!');
    return {
        aiInstructions: 'You are a helpful customer service representative. Greet the caller warmly and ask how you can help them today.',
        voice: validVoice,
        model: validModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'inbound',
        scriptId: 'customer-service'
    };
}

// 3. OUTBOUND TESTING Configuration
export async function getOutboundTestConfig(deps: WebSocketDependencies): Promise<SessionConfig> {
    try {
        console.log('🔍 DEBUG: Getting current outbound script...');
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        console.log('🔍 DEBUG: Current outbound script:', !!currentScript, currentScript);
        if (currentScript) {
            console.log('🔍 DEBUG: Getting script config for outbound ID:', currentScript);
            const config = await deps.scriptManager.getScriptConfig(currentScript, false);
            console.log('🔍 DEBUG: Outbound script config result:', !!config, config?.aiInstructions?.length);
            if (config && config.aiInstructions) {
                console.log(`✅ [OUTBOUND TEST] Using real campaign script: ${config.aiInstructions.substring(0, 100)}...`);
                
                // Validate AI instructions before returning
                if (config.aiInstructions.trim().length === 0) {
                    console.error(`❌ [OUTBOUND TEST] Campaign script has empty AI instructions`);
                    throw new Error('Outbound test config has empty AI instructions');
                }
                
                return {
                    ...config,
                    scriptId: String(config.scriptId),
                    isTestMode: true
                };
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting outbound test config:', error);
    }

    // CRITICAL FIX: Load real campaign script instead of generic instructions
    console.log(`🔧 [OUTBOUND TEST] Loading real campaign script as fallback...`);

    try {
        // Load campaign script 1 (outbound) as default for testing
        const scriptStartTime = Date.now();
        const campaignScript = await deps.scriptManager.getScriptConfig(1, false); // ID 1 = outbound campaign 1
        const scriptLoadTime = Date.now() - scriptStartTime;
        console.log(`⏱️ [OUTBOUND TEST] Script loading took ${scriptLoadTime}ms`);
        
        if (campaignScript && campaignScript.aiInstructions) {
            console.log(`✅ [OUTBOUND TEST] Using real campaign script 1: ${campaignScript.aiInstructions.substring(0, 100)}...`);
            
            // Validate AI instructions before returning
            if (campaignScript.aiInstructions.trim().length === 0) {
                console.error(`❌ [OUTBOUND TEST] Campaign script 1 has empty AI instructions`);
                throw new Error('Outbound test fallback config has empty AI instructions');
            }
            
            return {
                ...campaignScript,
                scriptId: String(campaignScript.scriptId),
                isTestMode: true
            };
        }
    } catch (scriptError) {
        console.error('❌ Error loading outbound campaign script:', scriptError);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // DEBUG: Check what the model manager is actually returning
    console.log(`🔍 DEBUG: deps.GEMINI_DEFAULT_MODEL = "${deps.GEMINI_DEFAULT_MODEL}"`);
    console.log(`🔍 DEBUG: validModel from manager = "${validModel}"`);
    console.log(`🔍 DEBUG: modelManager.getDefaultModel() = "${deps.modelManager.getDefaultModel()}"`);
    console.log(`🔍 DEBUG: process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);

    // CRITICAL ERROR: No campaign script available - this should not happen
    console.error('❌ [OUTBOUND TEST] CRITICAL: No campaign script could be loaded! Testing requires valid scripts.');
    throw new Error('No outbound campaign script available for testing. Please ensure campaign scripts are properly configured.');
}

// 4. INBOUND TESTING Configuration
export async function getInboundTestConfig(deps: WebSocketDependencies): Promise<SessionConfig> {
    try {
        console.log(`🔍 DEBUG: Getting current incoming script...`);
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        console.log(`🔍 DEBUG: Current script:`, !!currentScript, currentScript);
        if (currentScript) {
            console.log(`🔍 DEBUG: Getting script config for ID: ${currentScript}`);
            const config = await deps.scriptManager.getScriptConfig(currentScript, true);
            console.log(`🔍 DEBUG: Script config result:`, !!config);
            if (config && config.aiInstructions) {
                console.log(`✅ [INBOUND TEST] Using real campaign script: ${config.aiInstructions.substring(0, 100)}...`);
                
                // Validate AI instructions before returning
                if (config.aiInstructions.trim().length === 0) {
                    console.error(`❌ [INBOUND TEST] Campaign script has empty AI instructions`);
                    throw new Error('Inbound test config has empty AI instructions');
                }
                
                return {
                    ...config,
                    scriptId: String(config.scriptId),
                    isTestMode: true
                };
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting inbound test config:', error);
    }

    // CRITICAL FIX: Load real campaign script instead of empty instructions
    console.log(`🔧 [INBOUND TEST] Loading real campaign script as fallback...`);

    try {
        // Load campaign script 1 (incoming) as default for testing
        const campaignScript = await deps.scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
        if (campaignScript && campaignScript.aiInstructions) {
            console.log(`✅ [INBOUND TEST] Using real campaign script 1: ${campaignScript.aiInstructions.substring(0, 100)}...`);
            
            // Validate AI instructions before returning
            if (campaignScript.aiInstructions.trim().length === 0) {
                console.error(`❌ [INBOUND TEST] Campaign script 1 has empty AI instructions`);
                throw new Error('Inbound test fallback config has empty AI instructions');
            }
            
            return {
                ...campaignScript,
                scriptId: String(campaignScript.scriptId),
                isTestMode: true
            };
        }
    } catch (scriptError) {
        console.error('❌ Error loading campaign script:', scriptError);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(getConfigValue('ai.gemini.defaultVoice', 'Kore'));
    const validModel = deps.modelManager.getValidGeminiModel(getConfigValue('ai.gemini.defaultModel'));

    // CRITICAL ERROR: No campaign script available - this should not happen
    console.error('❌ [INBOUND TEST] CRITICAL: No campaign script could be loaded! Testing requires valid scripts.');
    throw new Error('No inbound campaign script available for testing. Please ensure campaign scripts are properly configured.');
}