// Shared API types for route modules
export interface Dependencies {
    sessionManager: any;
    contextManager: any;
    activeConnections: Map<string, any>;
    healthMonitor?: any;
    recoveryManager?: any;
    lifecycleManager?: any;
    summaryManager?: any;
    scriptManager: any;
    voiceManager: any;
    modelManager: any;
    GEMINI_DEFAULT_VOICE: string;
    GEMINI_DEFAULT_MODEL: string;
    SUMMARY_GENERATION_PROMPT: string;
}

export interface CallConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName: string | null;
    targetPhoneNumber: string | null;
    callSid?: string;
    from?: string;
    to?: string;
    callStatus?: string;
    isIncomingCall?: boolean;
    timestamp?: string;
    scriptType?: string;
    scriptId?: string;
    phoneNumber: string;
    mode: 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
}

export interface CallStatusBody {
    CallSid: string;
    CallStatus: string;
    Duration?: string;
    From?: string;
    To?: string;
}

export interface ConfigureCallBody {
    aiInstructions?: string;
    voice?: string;
    model?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}

export interface EndSessionParams {
    callSid: string;
}

export interface SetVoiceBody {
    voice: string;
}

export interface SetModelBody {
    model: string;
}

export interface AudioSettingsBody {
    [key: string]: any;
}

export interface TriggerRecoveryParams {
    callSid: string;
}

export interface CampaignScriptParams {
    id: string;
}

export interface IncomingCallBody {
    CallSid: string;
    From: string;
    To: string;
    CallStatus: string;
    AccountSid: string;
    ApiVersion: string;
    Direction: string;
    ForwardedFrom?: string;
    CallerName?: string;
}