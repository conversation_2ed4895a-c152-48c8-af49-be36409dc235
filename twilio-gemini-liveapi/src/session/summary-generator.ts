import { sessionLogger } from '../utils/logger';
import { ConversationEntry } from '../types/global';

export function generateLocalSummary(conversationLog: ConversationEntry[], summaryPrompt: string): string {
    try {
        if (!conversationLog || conversationLog.length === 0) {
            return 'No conversation content available.';
        }

        const totalMessages = conversationLog.length;
        const userMessages = conversationLog.filter(m => m.role === 'user').length;
        const aiMessages = conversationLog.filter(m => m.role === 'assistant').length;

        const firstMessages = conversationLog.slice(0, 3).map(m =>
            `${m.role}: ${m.content?.substring(0, 100) || 'Audio message'}`
        ).join('\n');

        const lastMessages = conversationLog.slice(-3).map(m =>
            `${m.role}: ${m.content?.substring(0, 100) || 'Audio message'}`
        ).join('\n');

        const summary = `Call Summary:\n` +
            `- Total messages: ${totalMessages} (${userMessages} from user, ${aiMessages} from AI)\n` +
            `- Duration: ${Math.round((Date.now() - (conversationLog[0]?.timestamp || Date.now())) / 1000)} seconds\n` +
            `- First exchanges:\n${firstMessages}\n` +
            `- Final exchanges:\n${lastMessages}\n` +
            `- Status: Conversation completed`;

        return summary;
    } catch (error) {
        sessionLogger.error('Error generating local summary:', error);
        return 'Error generating summary from conversation log.';
    }
}
