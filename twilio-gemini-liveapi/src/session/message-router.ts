import { forwardAudio } from '../audio/audio-forwarding';
import { AudioProcessor } from '../audio/audio-processor';
import { sessionLogger } from '../utils/logger';
import type { ConnectionData, GeminiLiveMessage, ConversationEntry } from '../types/global';

interface ExtendedConnectionData extends ConnectionData {
    conversationLog?: ConversationEntry[];
    summaryRequested?: boolean;
    summaryText?: string;
    lastAIResponse?: number;
    responseTimeouts?: number;
    connectionQuality?: string;
}

interface MessageRouterDeps {
    audioProcessor: AudioProcessor;
    activeConnections?: Map<string, ConnectionData> | null;
    sessionMetrics?: Map<string, { messagesReceived: number; lastActivity: number }>;
    forwardAudioFn?: typeof forwardAudio;
}

export async function routeGeminiMessage(
    callSid: string,
    message: GeminiLiveMessage,
    connectionData: ExtendedConnectionData,
    deps: MessageRouterDeps
): Promise<void> {
    const {
        audioProcessor,
        activeConnections,
        sessionMetrics,
        forwardAudioFn = forwardAudio
    } = deps;

    try {
        const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
        if (!message || !message.serverContent) {
            sessionLogger.warn(`⚠️ [${callSid}] Received invalid message structure`);
            return;
        }

        const metrics = sessionMetrics?.get(callSid);
        if (metrics) {
            metrics.messagesReceived = (metrics.messagesReceived || 0) + 1;
            metrics.lastActivity = Date.now();
        }

        if (hasAudio && hasAudio.mimeType && hasAudio.mimeType.includes('audio')) {
            if (!hasAudio.data || hasAudio.data.length === 0) {
                sessionLogger.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
            } else {
                const fresh = activeConnections?.get(callSid) || connectionData;
                await forwardAudioFn(callSid, hasAudio, fresh, audioProcessor);
            }
        }

        const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
        if (text) {
            connectionData.lastAIResponse = Date.now();
            connectionData.responseTimeouts = 0;
            connectionData.connectionQuality = 'good';

            if (connectionData.conversationLog) {
                const MAX_LOG_SIZE = 500;
                connectionData.conversationLog.push({ role: 'assistant', content: text, timestamp: Date.now() });
                if (connectionData.conversationLog.length > MAX_LOG_SIZE) {
                    connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_LOG_SIZE);
                }
            }

            if (connectionData.summaryRequested) {
                connectionData.summaryText = (connectionData.summaryText || '') + text;
            }
        }
    } catch (error) {
        sessionLogger.error(`❌ [${callSid}] Error processing Gemini message:`, error);
    }
}
