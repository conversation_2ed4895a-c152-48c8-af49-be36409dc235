module.exports = {
    apps: [
        {
            name: 'twilio-gemini-backend',
            script: 'npm',
            args: 'run dev',
            env_file: '.env',
            env: {
                PORT: 3101,
                NODE_ENV: 'production',
                PUBLIC_URL: 'https://gemini-api.verduona.com',
                CORS_ORIGIN: 'https://twilio-gemini.verduona.com',
                GEMINI_API_KEY: 'AIzaSyCaFEQUOGqa9HrhU0gqhl1wNuoOwcpodAg',
                TWILIO_ACCOUNT_SID: '**********************************',
                TWILIO_AUTH_TOKEN: '8e2cee6f53d27a8dc7f4ef883af228ef',
                TWILIO_PHONE_NUMBER: '+***********'
            }
        },
        {
            name: 'twilio-gemini-frontend',
            cwd: './call-center-frontend',
            script: 'npm',
            args: 'start',
            env: {
                PORT: 3011,
                NODE_ENV: 'production',
                NEXT_PUBLIC_BACKEND_URL: 'https://gemini-api.verduona.com'
            }
        }
    ]
};
